/**
 * Comprehensive Browser Extension Error Suppression
 * Prevents browser extension errors from polluting the console
 * and interfering with application functionality
 */

(function() {
  'use strict';

  // Store original console methods
  const originalConsoleError = console.error;
  const originalConsoleWarn = console.warn;
  const originalConsoleLog = console.log;

  // Comprehensive patterns for browser extension errors
  const EXTENSION_ERROR_PATTERNS = [
    // Chrome extension errors
    'runtime.lastError',
    'unchecked runtime.lastError',
    'the message port closed before a response was received',
    'message port closed',
    'extension context invalidated',
    'chrome-extension://',
    'could not establish connection',
    'receiving end does not exist',
    'extension context',
    'chrome.runtime',
    'browser.runtime',
    'cannot access chrome://',
    'cannot access about:',
    
    // Firefox extension errors
    'moz-extension://',
    'webextension',
    
    // Safari extension errors
    'safari-extension://',
    'safari-web-extension://',
    
    // Generic extension errors
    'script error',
    'non-error promise rejection captured',
    'loading css chunk',
    'loading chunk',
    'dynamically imported module',
    'failed to fetch dynamically imported module',
    'network error when attempting to fetch resource',
    
    // Ad blocker and privacy extension errors
    'adblock',
    'ublock',
    'ghostery',
    'privacy badger',
    'disconnect',
    'duckduckgo',
    
    // Password manager extension errors
    'lastpass',
    '1password',
    'bitwarden',
    'dashlane',
    'keeper',
    
    // Other common extension errors
    'grammarly',
    'honey',
    'metamask',
    'web3',
    'coinbase wallet'
  ];

  // Additional patterns for specific error types
  const RUNTIME_ERROR_PATTERNS = [
    /unchecked runtime\.lastError/i,
    /the message port closed before a response was received/i,
    /could not establish connection\. receiving end does not exist/i,
    /extension context invalidated/i,
    /chrome-extension:\/\//i,
    /moz-extension:\/\//i,
    /safari-extension:\/\//i
  ];

  /**
   * Check if an error message matches extension error patterns
   * @param {string} message - The error message to check
   * @returns {boolean} - True if it's an extension error
   */
  function isExtensionError(message) {
    if (typeof message !== 'string') {
      message = String(message);
    }
    
    const lowerMessage = message.toLowerCase();
    
    // Check against text patterns
    const hasTextPattern = EXTENSION_ERROR_PATTERNS.some(pattern => 
      lowerMessage.includes(pattern.toLowerCase())
    );
    
    // Check against regex patterns
    const hasRegexPattern = RUNTIME_ERROR_PATTERNS.some(pattern => 
      pattern.test(message)
    );
    
    return hasTextPattern || hasRegexPattern;
  }

  /**
   * Filter console method to suppress extension errors
   * @param {Function} originalMethod - Original console method
   * @param {string} methodName - Name of the console method
   * @returns {Function} - Filtered console method
   */
  function createFilteredConsoleMethod(originalMethod, methodName) {
    return function(...args) {
      // Convert all arguments to a single message string
      const message = args.map(arg => {
        if (typeof arg === 'object') {
          try {
            return JSON.stringify(arg);
          } catch (e) {
            return String(arg);
          }
        }
        return String(arg);
      }).join(' ');

      // Check if this is an extension error
      if (isExtensionError(message)) {
        // Silently suppress extension errors
        // Optionally, you can log them to a separate debug channel
        if (window.DEBUG_EXTENSION_ERRORS) {
          originalMethod.call(console, `[SUPPRESSED ${methodName.toUpperCase()}]`, ...args);
        }
        return;
      }

      // Log non-extension errors normally
      originalMethod.apply(console, args);
    };
  }

  /**
   * Suppress window.onerror for extension errors
   */
  function suppressWindowErrors() {
    const originalOnError = window.onerror;
    
    window.onerror = function(message, source, lineno, colno, error) {
      // Check if this is an extension error
      if (isExtensionError(message) || 
          (source && (source.includes('chrome-extension://') || 
                     source.includes('moz-extension://') || 
                     source.includes('safari-extension://')))) {
        // Suppress extension errors
        return true; // Prevents the error from being logged
      }
      
      // Call original error handler for non-extension errors
      if (originalOnError) {
        return originalOnError.call(this, message, source, lineno, colno, error);
      }
      
      return false; // Let the error be handled normally
    };
  }

  /**
   * Suppress unhandled promise rejections from extensions
   */
  function suppressUnhandledRejections() {
    const originalHandler = window.onunhandledrejection;
    
    window.addEventListener('unhandledrejection', function(event) {
      const reason = event.reason;
      let message = '';
      
      if (reason && reason.message) {
        message = reason.message;
      } else if (typeof reason === 'string') {
        message = reason;
      } else {
        message = String(reason);
      }
      
      // Check if this is an extension error
      if (isExtensionError(message)) {
        event.preventDefault(); // Suppress the error
        return;
      }
      
      // Call original handler for non-extension errors
      if (originalHandler) {
        originalHandler.call(this, event);
      }
    });
  }

  /**
   * Initialize error suppression
   */
  function initializeErrorSuppression() {
    try {
      // Override console methods
      console.error = createFilteredConsoleMethod(originalConsoleError, 'error');
      console.warn = createFilteredConsoleMethod(originalConsoleWarn, 'warn');
      console.log = createFilteredConsoleMethod(originalConsoleLog, 'log');
      
      // Suppress window errors
      suppressWindowErrors();
      
      // Suppress unhandled promise rejections
      suppressUnhandledRejections();
      
      // Store original methods for potential restoration
      window.__originalConsole = {
        error: originalConsoleError,
        warn: originalConsoleWarn,
        log: originalConsoleLog
      };
      
      console.log('🛡️ Extension error suppression initialized');
      
    } catch (error) {
      // If initialization fails, restore original console methods
      console.error = originalConsoleError;
      console.warn = originalConsoleWarn;
      console.log = originalConsoleLog;
      
      originalConsoleError('Failed to initialize extension error suppression:', error);
    }
  }

  /**
   * Restore original console methods (for debugging)
   */
  window.restoreConsole = function() {
    if (window.__originalConsole) {
      console.error = window.__originalConsole.error;
      console.warn = window.__originalConsole.warn;
      console.log = window.__originalConsole.log;
      console.log('Console methods restored to original state');
    }
  };

  /**
   * Enable debug mode for extension errors
   */
  window.debugExtensionErrors = function(enable = true) {
    window.DEBUG_EXTENSION_ERRORS = enable;
    console.log(`Extension error debugging ${enable ? 'enabled' : 'disabled'}`);
  };

  // Initialize when DOM is ready or immediately if already loaded
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeErrorSuppression);
  } else {
    initializeErrorSuppression();
  }

})();
