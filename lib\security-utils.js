/**
 * Security utilities for production-safe operations
 * Handles logging, error reporting, and sensitive data protection
 */

const isProduction = process.env.NODE_ENV === 'production'
const isDevMode = process.env.NEXT_PUBLIC_DEV_MODE === 'true'
const disableConsoleLogs = process.env.NEXT_PUBLIC_DISABLE_CONSOLE_LOGS === 'true'

/**
 * Production-safe logging that filters sensitive information
 */
export const secureLog = {
  info: (...args) => {
    if (!isProduction || isDevMode) {
      console.log(...args)
    }
  },
  
  warn: (...args) => {
    if (!isProduction || isDevMode) {
      console.warn(...args)
    }
  },
  
  error: (...args) => {
    // Always log errors, but filter sensitive data in production
    if (isProduction && !isDevMode) {
      const filteredArgs = args.map(arg => filterSensitiveData(arg))
      console.error(...filteredArgs)
    } else {
      console.error(...args)
    }
  },
  
  debug: (...args) => {
    // Only log debug info in development
    if (!isProduction) {
      console.debug(...args)
    }
  }
}

/**
 * Filter sensitive data from logs
 */
function filterSensitiveData(data) {
  if (typeof data === 'string') {
    return data
      .replace(/Bearer\s+[A-Za-z0-9\-_]+\.[A-Za-z0-9\-_]+\.[A-Za-z0-9\-_]+/g, 'Bearer [FILTERED]')
      .replace(/password['":\s]*['"]\w+['"]/gi, 'password: "[FILTERED]"')
      .replace(/token['":\s]*['"]\w+['"]/gi, 'token: "[FILTERED]"')
      .replace(/key['":\s]*['"]\w+['"]/gi, 'key: "[FILTERED]"')
      .replace(/[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/g, '[EMAIL_FILTERED]')
  }
  
  if (typeof data === 'object' && data !== null) {
    const filtered = { ...data }
    const sensitiveKeys = ['password', 'token', 'key', 'secret', 'auth', 'bearer']
    
    Object.keys(filtered).forEach(key => {
      if (sensitiveKeys.some(sensitive => key.toLowerCase().includes(sensitive))) {
        filtered[key] = '[FILTERED]'
      }
    })
    
    return filtered
  }
  
  return data
}

/**
 * Check if admin access should be allowed
 */
export function isAdminAccessAllowed(user, role) {
  // In production, be more strict about admin access
  if (isProduction && !isDevMode) {
    // Only allow specific verified admin users
    const verifiedAdmins = [
      '<EMAIL>',
      '<EMAIL>'
    ]
    
    return verifiedAdmins.includes(user?.email) && ['admin', 'staff'].includes(role)
  }
  
  // In development, allow more flexibility
  return ['admin', 'staff'].includes(role)
}

/**
 * Sanitize error messages for production
 */
export function sanitizeErrorMessage(error) {
  if (!isProduction) {
    return error.message || error.toString()
  }
  
  // In production, return generic error messages to avoid information leakage
  const genericMessages = {
    'authentication': 'Authentication failed',
    'authorization': 'Access denied',
    'database': 'Database operation failed',
    'network': 'Network error occurred',
    'validation': 'Invalid input provided'
  }
  
  const errorString = error.message || error.toString()
  
  for (const [type, message] of Object.entries(genericMessages)) {
    if (errorString.toLowerCase().includes(type)) {
      return message
    }
  }
  
  return 'An error occurred'
}

/**
 * Check if development features should be enabled
 */
export function isDevelopmentFeatureEnabled() {
  return !isProduction || isDevMode
}

/**
 * Get safe environment info for client-side
 */
export function getSafeEnvironmentInfo() {
  return {
    isProduction,
    isDevelopment: !isProduction,
    version: process.env.npm_package_version || '1.0.0',
    // Don't expose sensitive environment variables
    features: {
      devMode: isDevMode,
      debugAuth: process.env.NEXT_PUBLIC_DEBUG_AUTH === 'true'
    }
  }
}

/**
 * Disable console methods in production for security
 */
export function disableProductionConsole() {
  if (typeof window !== 'undefined' && isProduction && disableConsoleLogs) {
    const noop = () => {}
    window.console.log = noop
    window.console.warn = noop
    window.console.info = noop
    window.console.debug = noop
    // Keep error logging for critical issues
    const originalError = window.console.error
    window.console.error = (...args) => {
      const filteredArgs = args.map(arg => filterSensitiveData(arg))
      originalError.apply(window.console, filteredArgs)
    }
  }
}
